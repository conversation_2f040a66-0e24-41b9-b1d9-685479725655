// BottomNavigation.tsx
// Alt navigasyon barı component'i

import React, { useState } from 'react';
import {
  Home,
  ShoppingCart,
  Users,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '../common/Button';
import { MoreModal } from './MoreModal';
import { cn } from '../../lib/utils';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  isActive?: boolean;
  onClick?: () => void;
}

export interface BottomNavigationProps {
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  onCreateOrder?: () => void;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab = 'home',
  onTabChange,
  onCreateOrder
}) => {
  const [isMoreModalOpen, setIsMoreModalOpen] = useState(false);

  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: '<PERSON><PERSON>',
      icon: <Home className="w-5 h-5" />,
      isActive: activeTab === 'home',
      onClick: () => onTabChange?.('home')
    },
    {
      id: 'orders',
      label: 'Siparişler',
      icon: <ShoppingCart className="w-5 h-5" />,
      isActive: activeTab === 'orders',
      onClick: () => onTabChange?.('orders')
    },
    {
      id: 'tables',
      label: 'Masalar',
      icon: <Users className="w-5 h-5" />,
      isActive: activeTab === 'tables',
      onClick: () => onTabChange?.('tables')
    },
    {
      id: 'more',
      label: 'Daha Fazla',
      icon: <MoreHorizontal className="w-5 h-5" />,
      isActive: false,
      onClick: () => setIsMoreModalOpen(true)
    }
  ];

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div className="flex items-center px-4 py-3 max-w-full">
          {navigationItems.map((item, index) => (
            <React.Fragment key={item.id}>
              {/* Navigation Item */}
              <button
                onClick={item.onClick}
                className={cn(
                  'flex items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 flex-1 min-w-0',
                  item.isActive
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                )}
              >
                <div className={cn(
                  'transition-transform duration-200 mr-2 flex-shrink-0',
                  item.isActive && 'scale-110'
                )}>
                  {item.icon}
                </div>
                <span className="text-sm font-medium truncate">
                  {item.label}
                </span>
                {item.isActive && (
                  <div className="w-1 h-1 bg-blue-600 rounded-full ml-2 flex-shrink-0" />
                )}
              </button>

              {/* Create Order Button (Floating Action Button) */}
              {index === 1 && (
                <div className="relative flex-shrink-0 mx-3">
                  <Button
                    variant="primary"
                    size="icon"
                    onClick={onCreateOrder}
                    className="w-16 h-16 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 -mt-4 border-4 border-white"
                  >
                    <div
                      className="flex items-center justify-center text-white"
                      style={{
                        width: '56px',
                        height: '56px'
                      }}
                    >
                      {/* Custom Utensils Icon - CSS Based */}
                      <div className="relative" style={{ width: '48px', height: '48px' }}>
                        {/* Fork */}
                        <div
                          className="absolute bg-white rounded-full"
                          style={{
                            width: '4px',
                            height: '40px',
                            left: '14px',
                            top: '4px',
                            transform: 'rotate(-15deg)'
                          }}
                        />
                        <div
                          className="absolute bg-white rounded-full"
                          style={{
                            width: '2px',
                            height: '12px',
                            left: '12px',
                            top: '2px',
                            transform: 'rotate(-15deg)'
                          }}
                        />
                        <div
                          className="absolute bg-white rounded-full"
                          style={{
                            width: '2px',
                            height: '12px',
                            left: '16px',
                            top: '2px',
                            transform: 'rotate(-15deg)'
                          }}
                        />

                        {/* Knife */}
                        <div
                          className="absolute bg-white rounded-full"
                          style={{
                            width: '4px',
                            height: '40px',
                            right: '14px',
                            top: '4px',
                            transform: 'rotate(15deg)'
                          }}
                        />
                        <div
                          className="absolute bg-white rounded"
                          style={{
                            width: '8px',
                            height: '16px',
                            right: '12px',
                            top: '2px',
                            transform: 'rotate(15deg)'
                          }}
                        />
                      </div>
                    </div>
                  </Button>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* More Modal */}
      <MoreModal
        isOpen={isMoreModalOpen}
        onClose={() => setIsMoreModalOpen(false)}
      />
    </>
  );
};
