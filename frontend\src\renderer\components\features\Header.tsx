// Header.tsx
// Ana header component'i - logo, kull<PERSON><PERSON><PERSON><PERSON> bilgileri, bi<PERSON><PERSON><PERSON>

import React, { useState, useEffect } from 'react';
import { Bell, LogOut, User, Clock } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { cn } from '../../lib/utils';

export interface HeaderProps {
  className?: string;
  onNotificationClick?: () => void;
  onLogout?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  className,
  onNotificationClick,
  onLogout
}) => {
  const { user, logout } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Real-time saat güncelleme
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    } else {
      logout();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <header className={cn(
      'bg-white border-b border-gray-200 shadow-sm px-4 py-3 flex items-center justify-between',
      className
    )}>
      {/* Sol Taraf - Logo ve Tarih/Saat */}
      <div className="flex items-center space-x-6">
        {/* Restoran Bilgileri */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center shadow-md">
            <span className="text-white font-bold text-lg">🍽️</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Atropos Restaurant</h1>
            <p className="text-xs text-gray-500">Kadıköy Şubesi • Masa Servisi</p>
          </div>
        </div>

        {/* Tarih ve Saat */}
        <div className="hidden md:flex items-center space-x-4 pl-6 border-l border-gray-200">
          <div className="flex items-center space-x-2 text-gray-600">
            <Clock className="w-4 h-4" />
            <div className="text-sm">
              <div className="font-semibold">{formatTime(currentTime)}</div>
              <div className="text-xs text-gray-500">{formatDate(currentTime)}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Sağ Taraf - Bildirimler ve Kullanıcı */}
      <div className="flex items-center space-x-4">
        {/* Bildirimler */}
        <button
          onClick={onNotificationClick}
          className="relative p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
        >
          <Bell className="w-5 h-5" />
          {/* Bildirim badge */}
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">3</span>
          </span>
        </button>

        {/* Kullanıcı Bilgileri */}
        <div className="flex items-center space-x-3 pl-4 border-l border-gray-200">
          {/* Avatar */}
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          
          {/* Kullanıcı Adı */}
          <div className="hidden sm:block">
            <div className="text-sm font-semibold text-gray-900">
              {user?.name || 'Kullanıcı'}
            </div>
            <div className="text-xs text-gray-500">
              {user?.role || 'Garson'}
            </div>
          </div>

          {/* Çıkış Butonu */}
          <button
            onClick={handleLogout}
            className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
            title="Çıkış Yap"
          >
            <LogOut className="w-4 h-4" />
          </button>
        </div>
      </div>
    </header>
  );
};
