// Dashboard.tsx
// Ana Dashboard sayfası

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  TrendingUp,
  Clock,
  Users,
  Search,
  Package,
  AlertTriangle,
  ShoppingCart
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/common/Card';
import { EmptyState } from '../components/common/EmptyState';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { SummaryCard } from '../components/features/SummaryCard';
import { PopularDishCard } from '../components/features/PopularDishCard';
import { OutOfStockCard } from '../components/features/OutOfStockCard';
import { OrderCard } from '../components/features/OrderCard';
import { BottomNavigation } from '../components/features/BottomNavigation';

import { useDashboardStore } from '../store/dashboardStore';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api.service';
import { OrderStatus } from '../types/dashboard.types';
import { cn } from '../lib/utils';

export const Dashboard: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<OrderStatus>('in_progress');
  const [currentNavTab, setCurrentNavTab] = useState('home');
  
  const { user } = useAuthStore();
  const {
    summary,
    popularDishes,
    outOfStockItems,
    orders,
    filters,
    loading,
    error,
    setSummary,
    setPopularDishes,
    setOutOfStockItems,
    setOrders,
    setFilters,
    setLoading,
    setError,
    clearError
  } = useDashboardStore();

  // Dashboard Summary Query
  const { data: summaryData, isLoading: summaryLoading } = useQuery({
    queryKey: ['dashboard-summary'],
    queryFn: async () => {
      const response = await apiService.getDashboardSummary();
      if (response.success && response.data) {
        setSummary(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Özet veriler alınamadı');
    },
    refetchInterval: 30000, // 30 saniyede bir güncelle
  });

  // Popular Dishes Query
  const { data: dishesData, isLoading: dishesLoading } = useQuery({
    queryKey: ['popular-dishes'],
    queryFn: async () => {
      const response = await apiService.getPopularDishes(10);
      if (response.success && response.data) {
        setPopularDishes(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Popüler yemekler alınamadı');
    },
    refetchInterval: 60000, // 1 dakikada bir güncelle
  });

  // Out of Stock Query
  const { data: stockData, isLoading: stockLoading } = useQuery({
    queryKey: ['out-of-stock'],
    queryFn: async () => {
      const response = await apiService.getOutOfStockItems();
      if (response.success && response.data) {
        setOutOfStockItems(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Stok bilgileri alınamadı');
    },
    refetchInterval: 120000, // 2 dakikada bir güncelle
  });

  // Orders Query
  const { data: ordersData, isLoading: ordersLoading, refetch: refetchOrders } = useQuery({
    queryKey: ['dashboard-orders', activeTab],
    queryFn: async () => {
      const response = await apiService.getDashboardOrders({
        status: activeTab,
        limit: 50
      });
      if (response.success && response.data) {
        setOrders(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Siparişler alınamadı');
    },
    refetchInterval: 15000, // 15 saniyede bir güncelle
  });

  // Filter orders based on search query
  const filteredOrders = orders.filter(order => 
    order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.tableNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTabChange = (tab: OrderStatus) => {
    setActiveTab(tab);
    setFilters({ orderStatus: tab });
  };

  const handleCreateNewOrder = () => {
    // TODO: Navigate to order creation page
    console.log('Create new order');
  };

  const handlePayNow = (orderId: string) => {
    // TODO: Navigate to payment page
    console.log('Pay now for order:', orderId);
  };

  const handleViewOrder = (orderId: string) => {
    // TODO: Navigate to order details page
    console.log('View order:', orderId);
  };

  const handleNavTabChange = (tabId: string) => {
    setCurrentNavTab(tabId);
    // TODO: Navigate to different pages based on tabId
    console.log('Navigate to:', tabId);
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 overflow-hidden flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="h-full p-3 xl:p-4 pb-24 space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="space-y-0.5">
              <h1 className="text-xl xl:text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                Dashboard
              </h1>
              <p className="text-xs xl:text-sm text-slate-600 font-medium">
                Hoş geldin, {user?.firstName} {user?.lastName}
              </p>
            </div>
          </div>

          {/* Main Layout Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 flex-1 min-h-0">
            {/* Left Sidebar */}
            <div className="space-y-4 flex flex-col min-h-0">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 xl:gap-4">
                <SummaryCard
                  title="Toplam Kazanç"
                  value={summary?.totalEarning || 0}
                  change={summary?.totalEarningChange || '0%'}
                  icon={<TrendingUp className="w-4 h-4" />}
                  isLoading={summaryLoading}
                  className="bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200/50 hover:shadow-lg hover:shadow-emerald-100/50 transition-all duration-300"
                />
                <SummaryCard
                  title="Devam Eden Siparişler"
                  value={summary?.inProgressOrders || 0}
                  change={summary?.inProgressOrdersChange || '0%'}
                  icon={<Clock className="w-4 h-4" />}
                  isLoading={summaryLoading}
                  className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50 hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300"
                />
                <SummaryCard
                  title="Ödeme Bekleyen"
                  value={summary?.waitingList || 0}
                  change={summary?.waitingListChange || '0%'}
                  icon={<Users className="w-4 h-4" />}
                  isLoading={summaryLoading}
                  className="bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200/50 hover:shadow-lg hover:shadow-amber-100/50 transition-all duration-300"
                />
              </div>

              {/* Popular Dishes and Out of Stock - Side by Side */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1 min-h-[400px]">
                {/* Popular Dishes */}
                <Card className="flex-1 flex flex-col min-h-[380px] bg-white/80 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-200/20">
                  <CardHeader className="pb-3 p-4">
                    <CardTitle className="text-base xl:text-lg font-bold text-slate-800 flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-2 shadow-md shadow-emerald-200/50">
                        <Package className="w-4 h-4 text-white" />
                      </div>
                      Popüler Yemekler
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 overflow-y-auto px-4 pb-4">
                    {dishesLoading ? (
                      <div className="flex items-center justify-center py-6">
                        <LoadingSpinner size="sm" />
                      </div>
                    ) : popularDishes.length === 0 ? (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Package className="w-6 h-6 text-slate-400" />
                        </div>
                        <p className="text-sm text-slate-500 font-medium">
                          Henüz popüler yemek verisi yok
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {popularDishes.slice(0, 4).map((dish, index) => (
                          <PopularDishCard
                            key={dish.id}
                            dish={dish}
                            rank={index + 1}
                            className="hover:shadow-lg hover:shadow-slate-200/50 hover:scale-[1.02] transition-all duration-200 border-slate-200/60"
                          />
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Out of Stock */}
                <Card className="flex-1 flex flex-col min-h-[380px] bg-white/80 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-200/20">
                  <CardHeader className="pb-3 p-4">
                    <CardTitle className="text-base xl:text-lg font-bold text-slate-800 flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center mr-2 shadow-md shadow-amber-200/50">
                        <AlertTriangle className="w-4 h-4 text-white" />
                      </div>
                      Stokta Olmayan
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 overflow-y-auto px-4 pb-4">
                    {stockLoading ? (
                      <div className="flex items-center justify-center py-6">
                        <LoadingSpinner size="sm" />
                      </div>
                    ) : outOfStockItems.length === 0 ? (
                      <div className="text-center py-6">
                        <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Package className="w-6 h-6 text-emerald-600" />
                        </div>
                        <p className="text-sm text-slate-600 font-medium">
                          Tüm ürünler stokta mevcut
                        </p>
                        <p className="text-xs text-emerald-600 mt-1">
                          Harika! Stok durumu iyi görünüyor.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {outOfStockItems.slice(0, 4).map((item) => (
                          <OutOfStockCard
                            key={item.id}
                            item={item}
                            className="hover:shadow-lg hover:shadow-slate-200/50 hover:scale-[1.02] transition-all duration-200 border-slate-200/60"
                          />
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Orders Section - Right Side Full Height */}
            <div className="space-y-4 flex flex-col min-h-[600px]">
              {/* Search and Tabs */}
              <Card className="flex-1 flex flex-col min-h-[580px] bg-white/80 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-200/20">
                <CardHeader className="pb-3 p-4">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <CardTitle className="text-base xl:text-lg font-bold text-slate-800 flex items-center">
                      <ShoppingCart className="w-4 h-4 mr-2 text-blue-600" />
                      Siparişler
                    </CardTitle>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-3.5 h-3.5" />
                      <input
                        type="text"
                        placeholder="Sipariş ara..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full sm:w-56 pl-9 pr-3 py-2 text-sm bg-slate-50/80 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 focus:bg-white transition-all duration-200 placeholder:text-slate-400"
                      />
                    </div>
                  </div>

                  {/* Tabs */}
                  <div className="flex space-x-1 bg-slate-100/80 p-1 rounded-lg backdrop-blur-sm">
                    <button
                      onClick={() => handleTabChange('in_progress')}
                      className={cn(
                        'flex-1 py-2 px-2 xl:px-3 text-xs xl:text-sm font-semibold rounded-md transition-all duration-200',
                        activeTab === 'in_progress'
                          ? 'bg-white text-blue-600 shadow-md shadow-blue-100/50'
                          : 'text-slate-600 hover:text-slate-800 hover:bg-white/50'
                      )}
                    >
                      <span className="hidden sm:inline">Devam Ediyor</span>
                      <span className="sm:hidden">Devam</span>
                      <span className="ml-1 px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs font-bold">
                        {summary?.inProgressOrders || 0}
                      </span>
                    </button>
                    <button
                      onClick={() => handleTabChange('waiting_for_payment')}
                      className={cn(
                        'flex-1 py-2 px-2 xl:px-3 text-xs xl:text-sm font-semibold rounded-md transition-all duration-200',
                        activeTab === 'waiting_for_payment'
                          ? 'bg-white text-blue-600 shadow-md shadow-blue-100/50'
                          : 'text-slate-600 hover:text-slate-800 hover:bg-white/50'
                      )}
                    >
                      <span className="hidden sm:inline">Ödeme Bekliyor</span>
                      <span className="sm:hidden">Ödeme</span>
                      <span className="ml-1 px-1.5 py-0.5 bg-amber-100 text-amber-700 rounded-full text-xs font-bold">
                        {summary?.waitingList || 0}
                      </span>
                    </button>
                  </div>
                </CardHeader>

                <CardContent className="flex-1 overflow-y-auto px-4 pb-4">
                  {ordersLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : filteredOrders.length === 0 ? (
                    <EmptyState
                      icon={<ShoppingCart className="w-12 h-12" />}
                      title="Sipariş Bulunamadı"
                      description={
                        searchQuery
                          ? "Arama kriterlerinize uygun sipariş bulunamadı."
                          : activeTab === 'in_progress'
                            ? "Şu anda devam eden sipariş bulunmuyor."
                            : "Ödeme bekleyen sipariş bulunmuyor."
                      }
                      actionLabel={!searchQuery ? "Yeni Sipariş Oluştur" : undefined}
                      onAction={!searchQuery ? handleCreateNewOrder : undefined}
                    />
                  ) : (
                    <div className="space-y-3">
                      {filteredOrders.map((order) => (
                        <OrderCard
                          key={order.id}
                          order={order}
                          status={activeTab}
                          onPayNow={handlePayNow}
                          onViewOrder={handleViewOrder}
                          className="hover:shadow-lg hover:shadow-slate-200/50 hover:scale-[1.01] transition-all duration-200 border-slate-200/60"
                        />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation
        activeTab={currentNavTab}
        onTabChange={handleNavTabChange}
        onCreateOrder={handleCreateNewOrder}
      />
    </div>
  );
};
